import { Injectable } from '@angular/core';
import { <PERSON>ttp<PERSON><PERSON>, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { CacheManagementService } from '../services/cache-management.service';

@Injectable()
export class CacheBusterInterceptor implements HttpInterceptor {

  constructor(private cacheManagementService: CacheManagementService) {}

  intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<any>> {
    let modifiedRequest = request;

    // GET istekleri için cache busting
    if (request.method === 'GET' && this.isApiRequest(request.url)) {
      modifiedRequest = this.addCacheBustingToGetRequest(request);
    }

    // POST/PUT/DELETE istekleri için cache invalidation
    return next.handle(modifiedRequest).pipe(
      tap(event => {
        if (event instanceof HttpResponse && this.isApiRequest(request.url)) {
          this.handleCacheInvalidation(request, event);
        }
      })
    );
  }

  private isApiRequest(url: string): boolean {
    return url.includes('/api/') || url.includes('localhost') || url.includes('gymproject');
  }

  private addCacheBustingToGetRequest(request: HttpRequest<any>): HttpRequest<any> {
    // URL'den entity type'ını çıkar
    const entityType = this.extractEntityTypeFromUrl(request.url);
    
    // Cache version'ı al
    const cacheVersion = this.cacheManagementService.getCacheVersion(entityType);
    
    // Timestamp ve cache version ekle
    const timestamp = Date.now();
    
    let params = request.params;
    params = params.set('_t', timestamp.toString());
    params = params.set('_v', cacheVersion.toString());

    // Cache-Control headers ekle
    const headers = request.headers
      .set('Cache-Control', 'no-cache, no-store, must-revalidate')
      .set('Pragma', 'no-cache')
      .set('Expires', '0');

    return request.clone({
      params: params,
      headers: headers
    });
  }

  private handleCacheInvalidation(request: HttpRequest<any>, response: HttpResponse<any>): void {
    // Sadece başarılı istekler için cache invalidation
    if (response.status >= 200 && response.status < 300) {
      const method = request.method;
      const entityType = this.extractEntityTypeFromUrl(request.url);
      
      if (method === 'POST' || method === 'PUT' || method === 'DELETE') {
        let operation: 'create' | 'update' | 'delete' = 'update';
        
        if (method === 'POST' && (request.url.includes('/add') || request.url.includes('/create'))) {
          operation = 'create';
        } else if (method === 'DELETE' || request.url.includes('/delete')) {
          operation = 'delete';
        }
        
        // İlişkili entity'leri de invalidate et
        this.cacheManagementService.invalidateRelatedEntities(entityType, operation);
        
        console.log(`🔄 Cache invalidated after ${method} ${entityType}`);
      }
    }
  }

  private extractEntityTypeFromUrl(url: string): string {
    // URL'den entity type'ını çıkar
    // Örnek: /api/member/getall -> member
    // Örnek: /api/membership/update -> membership
    
    const urlParts = url.split('/');
    const apiIndex = urlParts.findIndex(part => part === 'api');
    
    if (apiIndex !== -1 && apiIndex + 1 < urlParts.length) {
      const entityPart = urlParts[apiIndex + 1];
      
      // Query string'i temizle
      const cleanEntity = entityPart.split('?')[0];
      
      // Özel durumlar için mapping
      const entityMapping: { [key: string]: string } = {
        'membershiptype': 'membershiptype',
        'memberentry': 'member',
        'workoutprogram': 'workout',
        'exercisecategory': 'exercise',
        'companyuser': 'user',
        'operationclaim': 'user',
        'licensepackage': 'company',
        'licensetransaction': 'company'
      };
      
      return entityMapping[cleanEntity.toLowerCase()] || cleanEntity.toLowerCase();
    }
    
    return 'unknown';
  }
}
