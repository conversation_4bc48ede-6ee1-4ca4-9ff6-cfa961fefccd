import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface CacheInvalidationEvent {
  entityType: string;
  operation: 'create' | 'update' | 'delete';
  timestamp: number;
}

@Injectable({
  providedIn: 'root'
})
export class CacheManagementService {
  private cacheVersions = new Map<string, number>();
  private invalidationSubject = new BehaviorSubject<CacheInvalidationEvent | null>(null);
  
  // Cache invalidation events'i dinlemek için
  public invalidationEvents$ = this.invalidationSubject.asObservable();

  constructor() {
    this.initializeDefaultVersions();
  }

  private initializeDefaultVersions(): void {
    // Tüm entity'ler için başlangıç versiyonları
    const entities = [
      'member', 'membership', 'product', 'transaction', 
      'payment', 'workout', 'exercise', 'company',
      'user', 'membershiptype', 'city', 'town'
    ];
    
    entities.forEach(entity => {
      this.cacheVersions.set(entity, 1);
    });
  }

  /**
   * Bel<PERSON>li bir entity'nin cache'ini invalidate et
   */
  invalidateEntity(entityType: string, operation: 'create' | 'update' | 'delete' = 'update'): void {
    const currentVersion = this.cacheVersions.get(entityType.toLowerCase()) || 1;
    const newVersion = currentVersion + 1;
    
    this.cacheVersions.set(entityType.toLowerCase(), newVersion);
    
    // Event yayınla
    const event: CacheInvalidationEvent = {
      entityType: entityType.toLowerCase(),
      operation,
      timestamp: Date.now()
    };
    
    this.invalidationSubject.next(event);
    
    console.log(`🗑️ Cache invalidated: ${entityType} (v${newVersion})`);
  }

  /**
   * Belirli bir entity'nin cache version'ını al
   */
  getCacheVersion(entityType: string): number {
    return this.cacheVersions.get(entityType.toLowerCase()) || 1;
  }

  /**
   * Tüm cache'i temizle
   */
  invalidateAll(): void {
    this.cacheVersions.forEach((version, entity) => {
      this.cacheVersions.set(entity, version + 1);
    });
    
    const event: CacheInvalidationEvent = {
      entityType: 'all',
      operation: 'update',
      timestamp: Date.now()
    };
    
    this.invalidationSubject.next(event);
    console.log('🗑️ All cache invalidated');
  }

  /**
   * İlişkili entity'lerin cache'ini de temizle
   */
  invalidateRelatedEntities(entityType: string, operation: 'create' | 'update' | 'delete' = 'update'): void {
    const relatedEntities = this.getRelatedEntities(entityType);
    
    // Ana entity'yi invalidate et
    this.invalidateEntity(entityType, operation);
    
    // İlişkili entity'leri de invalidate et
    relatedEntities.forEach(related => {
      this.invalidateEntity(related, operation);
    });
  }

  /**
   * Entity'nin ilişkili olduğu diğer entity'leri döndür
   */
  private getRelatedEntities(entityType: string): string[] {
    const relations: { [key: string]: string[] } = {
      'member': ['membership', 'payment', 'transaction', 'workout'],
      'membership': ['member', 'payment', 'membershiptype'],
      'payment': ['member', 'membership', 'transaction'],
      'transaction': ['member', 'payment', 'product'],
      'product': ['transaction'],
      'workout': ['member', 'exercise'],
      'exercise': ['workout'],
      'company': ['member', 'membership', 'user'],
      'user': ['member', 'company'],
      'membershiptype': ['membership']
    };
    
    return relations[entityType.toLowerCase()] || [];
  }

  /**
   * Cache statistics
   */
  getCacheStatistics(): { [entity: string]: number } {
    const stats: { [entity: string]: number } = {};
    this.cacheVersions.forEach((version, entity) => {
      stats[entity] = version;
    });
    return stats;
  }
}
